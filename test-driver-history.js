#!/usr/bin/env node

/**
 * Driver History Test Suite
 * Tests the historical driver tracking functionality
 * 
 * Usage: node test-driver-history.js
 * 
 * Tests:
 * 1. Database migration application
 * 2. Trip creation with active shift driver
 * 3. Shift changes during trip progression
 * 4. Trip completion with different driver
 * 5. Historical data preservation
 * 6. Display logic validation
 */

const { Pool } = require('pg');
const axios = require('axios');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_system',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

// API configuration
const API_BASE = 'http://localhost:5000/api';
const FRONTEND_BASE = 'http://localhost:3000';

// Test data
const TEST_DATA = {
  truck: { id: 1, truck_number: 'DT-100' },
  drivers: [
    { id: 1, name: '<PERSON>', employee_id: 'EMP001' },
    { id: 2, name: '<PERSON>', employee_id: 'EMP002' }
  ],
  locations: [
    { id: 1, name: 'Loading Point A', type: 'loading' },
    { id: 2, name: 'Unloading Point B', type: 'unloading' }
  ]
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    testResults.tests.push({ status: 'PASS', message });
    log(`PASS: ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.tests.push({ status: 'FAIL', message });
    log(`FAIL: ${message}`, 'error');
    throw new Error(`Assertion failed: ${message}`);
  }
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testDatabaseMigration() {
  log('Testing database migration...', 'info');
  
  try {
    // Check if new columns exist
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name LIKE 'performed_by_%'
      ORDER BY column_name
    `);
    
    const expectedColumns = [
      'performed_by_driver_id',
      'performed_by_driver_name', 
      'performed_by_employee_id',
      'performed_by_shift_id',
      'performed_by_shift_type'
    ];
    
    const actualColumns = columnsResult.rows.map(row => row.column_name);
    
    for (const expectedCol of expectedColumns) {
      assert(
        actualColumns.includes(expectedCol),
        `Column ${expectedCol} exists in trip_logs table`
      );
    }
    
    // Check if indexes exist
    const indexesResult = await pool.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'trip_logs' 
        AND indexname LIKE 'idx_trip_logs_performed_by_%'
    `);
    
    assert(
      indexesResult.rows.length >= 3,
      'Performance indexes created for driver history columns'
    );
    
    // Check if helper function exists
    const functionResult = await pool.query(`
      SELECT proname 
      FROM pg_proc 
      WHERE proname = 'capture_active_driver_for_trip'
    `);
    
    assert(
      functionResult.rows.length > 0,
      'Helper function capture_active_driver_for_trip exists'
    );
    
    log('Database migration test completed successfully', 'success');
    
  } catch (error) {
    log(`Database migration test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function setupTestData() {
  log('Setting up test data...', 'info');
  
  try {
    // Create test shifts for both drivers
    const today = new Date().toISOString().split('T')[0];
    
    // Day shift (8:00 - 16:00) for Driver 1
    await pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES ($1, $2, 'day', $3, '08:00:00', '16:00:00', 'active')
      ON CONFLICT (truck_id, shift_date, start_time) DO NOTHING
    `, [TEST_DATA.truck.id, TEST_DATA.drivers[0].id, today]);
    
    // Night shift (16:00 - 00:00) for Driver 2
    await pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES ($1, $2, 'night', $3, '16:00:00', '23:59:59', 'scheduled')
      ON CONFLICT (truck_id, shift_date, start_time) DO NOTHING
    `, [TEST_DATA.truck.id, TEST_DATA.drivers[1].id, today]);
    
    // Create test assignment
    const assignmentResult = await pool.query(`
      INSERT INTO assignments (truck_id, loading_location_id, unloading_location_id, status, assignment_code)
      VALUES ($1, $2, $3, 'assigned', 'TEST-DRIVER-HIST-001')
      ON CONFLICT (assignment_code) DO UPDATE SET status = 'assigned'
      RETURNING id
    `, [TEST_DATA.truck.id, TEST_DATA.locations[0].id, TEST_DATA.locations[1].id]);
    
    TEST_DATA.assignment_id = assignmentResult.rows[0].id;
    
    log('Test data setup completed', 'success');
    
  } catch (error) {
    log(`Test data setup failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testTripCreationWithActiveDriver() {
  log('Testing trip creation with active shift driver...', 'info');
  
  try {
    // Simulate QR scan at loading location during day shift
    const scanData = {
      type: 'location',
      location_id: TEST_DATA.locations[0].id,
      truck_id: TEST_DATA.truck.id
    };
    
    // Make API call to scanner endpoint
    const response = await axios.post(`${API_BASE}/scanner/scan`, scanData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    assert(response.status === 200, 'Scanner API call successful');
    assert(response.data.success === true, 'Scanner response indicates success');
    
    // Get the created trip
    const tripResult = await pool.query(`
      SELECT 
        id, trip_number, status,
        performed_by_driver_id,
        performed_by_driver_name,
        performed_by_employee_id,
        performed_by_shift_id,
        performed_by_shift_type,
        loading_start_time
      FROM trip_logs 
      WHERE assignment_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [TEST_DATA.assignment_id]);
    
    assert(tripResult.rows.length > 0, 'Trip was created');
    
    const trip = tripResult.rows[0];
    TEST_DATA.trip_id = trip.id;
    
    assert(trip.status === 'loading_start', 'Trip status is loading_start');
    assert(trip.performed_by_driver_id === TEST_DATA.drivers[0].id, 'Historical driver ID captured');
    assert(trip.performed_by_driver_name === TEST_DATA.drivers[0].name, 'Historical driver name captured');
    assert(trip.performed_by_employee_id === TEST_DATA.drivers[0].employee_id, 'Historical employee ID captured');
    assert(trip.performed_by_shift_type === 'day', 'Historical shift type captured');
    assert(trip.loading_start_time !== null, 'Loading start time recorded');
    
    log('Trip creation with active driver test completed successfully', 'success');
    
  } catch (error) {
    log(`Trip creation test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testShiftChangesDuringTrip() {
  log('Testing shift changes during trip progression...', 'info');
  
  try {
    // Change active shift to night driver
    await pool.query(`
      UPDATE driver_shifts 
      SET status = 'completed' 
      WHERE truck_id = $1 AND driver_id = $2 AND shift_type = 'day'
    `, [TEST_DATA.truck.id, TEST_DATA.drivers[0].id]);
    
    await pool.query(`
      UPDATE driver_shifts 
      SET status = 'active' 
      WHERE truck_id = $1 AND driver_id = $2 AND shift_type = 'night'
    `, [TEST_DATA.truck.id, TEST_DATA.drivers[1].id]);
    
    // Progress trip to loading_end
    const scanData = {
      type: 'location',
      location_id: TEST_DATA.locations[0].id,
      truck_id: TEST_DATA.truck.id
    };
    
    const response = await axios.post(`${API_BASE}/scanner/scan`, scanData);
    assert(response.status === 200, 'Loading end scan successful');
    
    // Check that historical driver info is preserved
    const tripResult = await pool.query(`
      SELECT 
        performed_by_driver_id,
        performed_by_driver_name,
        performed_by_shift_type,
        status
      FROM trip_logs 
      WHERE id = $1
    `, [TEST_DATA.trip_id]);
    
    const trip = tripResult.rows[0];
    
    assert(trip.status === 'loading_end', 'Trip progressed to loading_end');
    assert(trip.performed_by_driver_id === TEST_DATA.drivers[0].id, 'Historical driver preserved after shift change');
    assert(trip.performed_by_driver_name === TEST_DATA.drivers[0].name, 'Historical driver name preserved');
    assert(trip.performed_by_shift_type === 'day', 'Historical shift type preserved');
    
    log('Shift changes during trip test completed successfully', 'success');
    
  } catch (error) {
    log(`Shift changes test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testTripCompletionWithDifferentDriver() {
  log('Testing trip completion with different driver...', 'info');
  
  try {
    // Continue trip progression to completion
    // Scan at unloading location
    let scanData = {
      type: 'location',
      location_id: TEST_DATA.locations[1].id,
      truck_id: TEST_DATA.truck.id
    };
    
    let response = await axios.post(`${API_BASE}/scanner/scan`, scanData);
    assert(response.status === 200, 'Unloading start scan successful');
    
    // Complete unloading
    response = await axios.post(`${API_BASE}/scanner/scan`, scanData);
    assert(response.status === 200, 'Unloading end scan successful');
    
    // Return to loading location for trip completion
    scanData.location_id = TEST_DATA.locations[0].id;
    response = await axios.post(`${API_BASE}/scanner/scan`, scanData);
    assert(response.status === 200, 'Trip completion scan successful');
    
    // Verify historical data is preserved
    const tripResult = await pool.query(`
      SELECT 
        performed_by_driver_id,
        performed_by_driver_name,
        performed_by_shift_type,
        status
      FROM trip_logs 
      WHERE id = $1
    `, [TEST_DATA.trip_id]);
    
    const trip = tripResult.rows[0];
    
    assert(trip.status === 'trip_completed', 'Trip completed successfully');
    assert(trip.performed_by_driver_id === TEST_DATA.drivers[0].id, 'Original driver preserved in completed trip');
    assert(trip.performed_by_driver_name === TEST_DATA.drivers[0].name, 'Original driver name preserved');
    assert(trip.performed_by_shift_type === 'day', 'Original shift type preserved');
    
    log('Trip completion with different driver test completed successfully', 'success');
    
  } catch (error) {
    log(`Trip completion test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function testDisplayLogicValidation() {
  log('Testing display logic validation...', 'info');
  
  try {
    // Test API endpoint for trip data
    const response = await axios.get(`${API_BASE}/trips?page=1&limit=10`);
    assert(response.status === 200, 'Trips API endpoint accessible');
    
    const trips = response.data.data;
    const testTrip = trips.find(t => t.id === TEST_DATA.trip_id);
    
    assert(testTrip !== undefined, 'Test trip found in API response');
    assert(testTrip.performed_by_driver_name === TEST_DATA.drivers[0].name, 'Historical driver in API response');
    assert(testTrip.current_shift_driver_name === TEST_DATA.drivers[1].name, 'Current driver in API response');
    
    // Test that completed trips show historical driver
    assert(
      testTrip.status === 'trip_completed' && testTrip.performed_by_driver_name,
      'Completed trip shows historical driver'
    );
    
    log('Display logic validation test completed successfully', 'success');
    
  } catch (error) {
    log(`Display logic test failed: ${error.message}`, 'error');
    throw error;
  }
}

async function cleanupTestData() {
  log('Cleaning up test data...', 'info');
  
  try {
    // Delete test trips
    await pool.query('DELETE FROM trip_logs WHERE assignment_id = $1', [TEST_DATA.assignment_id]);
    
    // Delete test assignment
    await pool.query('DELETE FROM assignments WHERE id = $1', [TEST_DATA.assignment_id]);
    
    // Delete test shifts
    await pool.query(`
      DELETE FROM driver_shifts 
      WHERE truck_id = $1 AND shift_date = CURRENT_DATE
    `, [TEST_DATA.truck.id]);
    
    log('Test data cleanup completed', 'success');
    
  } catch (error) {
    log(`Cleanup failed: ${error.message}`, 'warning');
  }
}

// Main test execution
async function runTests() {
  log('🚀 Starting Driver History Test Suite', 'info');
  log(`Testing against API: ${API_BASE}`, 'info');
  log(`Frontend URL: ${FRONTEND_BASE}`, 'info');
  
  try {
    await testDatabaseMigration();
    await setupTestData();
    await testTripCreationWithActiveDriver();
    await testShiftChangesDuringTrip();
    await testTripCompletionWithDifferentDriver();
    await testDisplayLogicValidation();
    
    log('🎉 All tests completed successfully!', 'success');
    
  } catch (error) {
    log(`❌ Test suite failed: ${error.message}`, 'error');
  } finally {
    await cleanupTestData();
    await pool.end();
    
    // Print test summary
    console.log('\n📊 Test Results Summary:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.tests
        .filter(t => t.status === 'FAIL')
        .forEach(t => console.log(`  - ${t.message}`));
    }
    
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testResults };
