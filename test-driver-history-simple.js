#!/usr/bin/env node

/**
 * Simple Driver History Test
 * Quick validation of driver history functionality
 * 
 * Usage: node test-driver-history-simple.js
 */

const { Pool } = require('pg');

// Load environment variables
require('dotenv').config();

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
  port: process.env.DB_PORT || 5432,
});

async function testMigration() {
  console.log('🔍 Testing database migration...');
  
  try {
    // Check if new columns exist
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name LIKE 'performed_by_%'
      ORDER BY column_name
    `);
    
    console.log('📋 Found columns:');
    result.rows.forEach(row => {
      console.log(`  ✅ ${row.column_name} (${row.data_type})`);
    });
    
    if (result.rows.length >= 5) {
      console.log('✅ Migration appears successful');
      return true;
    } else {
      console.log('❌ Migration incomplete');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Migration test failed:', error.message);
    return false;
  }
}

async function testHelperFunction() {
  console.log('\n🔍 Testing helper function...');
  
  try {
    // Test the helper function
    const result = await pool.query(`
      SELECT * FROM capture_active_driver_for_trip(1)
    `);

    console.log('📋 Helper function result:');
    if (result.rows.length > 0) {
      console.log('  ✅ Function returns data:', result.rows[0]);
    } else {
      console.log('  ⚠️ Function returns no data (no active driver)');
    }

    return true;

  } catch (error) {
    console.log('❌ Helper function test failed:', error.message);
    return false;
  }
}

async function testTrigger() {
  console.log('\n🔍 Testing automatic trigger...');
  
  try {
    // Check if trigger exists
    const triggerResult = await pool.query(`
      SELECT trigger_name 
      FROM information_schema.triggers 
      WHERE event_object_table = 'trip_logs' 
        AND trigger_name = 'trigger_auto_capture_trip_driver'
    `);
    
    if (triggerResult.rows.length > 0) {
      console.log('  ✅ Trigger exists');
      return true;
    } else {
      console.log('  ❌ Trigger not found');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Trigger test failed:', error.message);
    return false;
  }
}

async function testExistingData() {
  console.log('\n🔍 Testing existing trip data...');
  
  try {
    // Check existing trips
    const result = await pool.query(`
      SELECT
        COUNT(*) as total_trips,
        COUNT(performed_by_driver_id) as trips_with_driver,
        ROUND((COUNT(performed_by_driver_id)::NUMERIC / COUNT(*) * 100), 2) as percentage
      FROM trip_logs
    `);
    
    const stats = result.rows[0];
    console.log('📊 Trip statistics:');
    console.log(`  📋 Total trips: ${stats.total_trips}`);
    console.log(`  👤 Trips with driver: ${stats.trips_with_driver}`);
    console.log(`  📈 Percentage: ${stats.percentage}%`);
    
    // Show sample data
    const sampleResult = await pool.query(`
      SELECT 
        id, trip_number, status,
        performed_by_driver_name,
        performed_by_shift_type,
        loading_start_time
      FROM trip_logs 
      WHERE performed_by_driver_id IS NOT NULL
      ORDER BY id DESC
      LIMIT 3
    `);
    
    if (sampleResult.rows.length > 0) {
      console.log('\n📋 Sample trips with driver data:');
      sampleResult.rows.forEach(trip => {
        console.log(`  🚚 Trip #${trip.trip_number}: ${trip.performed_by_driver_name} (${trip.performed_by_shift_type || 'unknown'} shift)`);
      });
    }
    
    return true;
    
  } catch (error) {
    console.log('❌ Existing data test failed:', error.message);
    return false;
  }
}

async function runSimpleTests() {
  console.log('🚀 Starting Simple Driver History Tests\n');
  
  const results = [];
  
  try {
    results.push(await testMigration());
    results.push(await testHelperFunction());
    results.push(await testTrigger());
    results.push(await testExistingData());
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${(passed/total*100).toFixed(1)}%`);
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Driver history system is ready.');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the migration.');
    }
    
  } catch (error) {
    console.log('❌ Test execution failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Run tests
if (require.main === module) {
  runSimpleTests().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { runSimpleTests };
