#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
  port: process.env.DB_PORT || 5432,
});

async function checkDatabase() {
  console.log('🔍 Checking database functions and migration status...');
  
  try {
    // Check if helper function exists
    console.log('\n📋 Checking helper function...');
    const functionResult = await pool.query(`
      SELECT proname, proargtypes, prosrc 
      FROM pg_proc 
      WHERE proname LIKE '%capture_active_driver%'
    `);
    
    if (functionResult.rows.length > 0) {
      console.log('✅ Helper function found:');
      functionResult.rows.forEach(func => {
        console.log(`  - ${func.proname}`);
      });
    } else {
      console.log('❌ Helper function not found');
      
      // Try to create the function manually
      console.log('🔧 Creating helper function...');
      await pool.query(`
        CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
            p_truck_id INTEGER,
            p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        RETURNS TABLE (
            driver_id INTEGER,
            driver_name VARCHAR(100),
            employee_id VARCHAR(20),
            shift_id INTEGER,
            shift_type shift_type
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                d.id as driver_id,
                d.full_name as driver_name,
                d.employee_id,
                ds.id as shift_id,
                ds.shift_type
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = p_truck_id
                AND ds.status = 'active'
                AND ds.shift_date = p_timestamp::date
                AND p_timestamp::time BETWEEN ds.start_time AND 
                    CASE 
                        WHEN ds.end_time < ds.start_time 
                        THEN ds.end_time + interval '24 hours'
                        ELSE ds.end_time 
                    END
            ORDER BY ds.created_at DESC
            LIMIT 1;
        END;
        $$ LANGUAGE plpgsql;
      `);
      console.log('✅ Helper function created');
    }
    
    // Test the helper function
    console.log('\n🧪 Testing helper function...');
    const testResult = await pool.query('SELECT * FROM capture_active_driver_for_trip(1, CURRENT_TIMESTAMP)');
    console.log(`✅ Helper function test completed (${testResult.rows.length} rows returned)`);
    
    // Check trip_logs columns
    console.log('\n📋 Checking trip_logs columns...');
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name LIKE 'performed_by_%'
      ORDER BY column_name
    `);
    
    console.log('✅ Driver history columns:');
    columnsResult.rows.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type})`);
    });
    
    // Check existing trip data
    console.log('\n📊 Checking existing trip data...');
    const tripStatsResult = await pool.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(performed_by_driver_id) as trips_with_driver
      FROM trip_logs
    `);
    
    const stats = tripStatsResult.rows[0];
    console.log(`📈 Total trips: ${stats.total_trips}`);
    console.log(`👤 Trips with driver: ${stats.trips_with_driver}`);
    
    // Sample trip data
    const sampleResult = await pool.query(`
      SELECT 
        id, trip_number, status,
        performed_by_driver_name,
        performed_by_shift_type,
        loading_start_time
      FROM trip_logs 
      ORDER BY id DESC
      LIMIT 3
    `);
    
    if (sampleResult.rows.length > 0) {
      console.log('\n📋 Sample trips:');
      sampleResult.rows.forEach(trip => {
        console.log(`  🚚 Trip #${trip.trip_number}: ${trip.performed_by_driver_name || 'No driver'} (${trip.status})`);
      });
    }
    
    console.log('\n🎉 Database check completed successfully!');
    
  } catch (error) {
    console.log('❌ Database check failed:', error.message);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  checkDatabase().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { checkDatabase };
