#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
  port: process.env.DB_PORT || 5432,
});

async function fixDatabaseFunctions() {
  console.log('🔧 Fixing database functions...');
  
  try {
    // Drop existing function if it exists
    console.log('🗑️ Dropping existing function...');
    await pool.query('DROP FUNCTION IF EXISTS capture_active_driver_for_trip CASCADE');
    
    // Create the function with correct signature
    console.log('🔧 Creating helper function with correct signature...');
    await pool.query(`
      CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
          p_truck_id INTEGER,
          p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
      RETURNS TABLE (
          driver_id INTEGER,
          driver_name VARCHAR(100),
          employee_id VARCHAR(20),
          shift_id INTEGER,
          shift_type shift_type
      ) AS $$
      BEGIN
          RETURN QUERY
          SELECT 
              d.id as driver_id,
              d.full_name as driver_name,
              d.employee_id,
              ds.id as shift_id,
              ds.shift_type
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = p_truck_id
              AND ds.status = 'active'
              AND ds.shift_date = p_timestamp::date
              AND p_timestamp::time BETWEEN ds.start_time AND 
                  CASE 
                      WHEN ds.end_time < ds.start_time 
                      THEN ds.end_time + interval '24 hours'
                      ELSE ds.end_time 
                  END
          ORDER BY ds.created_at DESC
          LIMIT 1;
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('✅ Helper function created successfully');
    
    // Test the function
    console.log('🧪 Testing helper function...');
    const testResult = await pool.query('SELECT * FROM capture_active_driver_for_trip(1)');
    console.log(`✅ Function test completed (${testResult.rows.length} rows returned)`);
    
    // Drop and recreate the trigger function
    console.log('🔧 Fixing trigger function...');
    await pool.query('DROP TRIGGER IF EXISTS trigger_auto_capture_trip_driver ON trip_logs');
    await pool.query('DROP FUNCTION IF EXISTS auto_capture_trip_driver CASCADE');
    
    await pool.query(`
      CREATE OR REPLACE FUNCTION auto_capture_trip_driver()
      RETURNS TRIGGER AS $$
      DECLARE
          driver_info RECORD;
          truck_id INTEGER;
      BEGIN
          -- Get truck_id from assignment
          SELECT a.truck_id INTO truck_id
          FROM assignments a
          WHERE a.id = NEW.assignment_id;
          
          -- Only capture driver info if not already set and trip is starting
          IF NEW.performed_by_driver_id IS NULL AND NEW.loading_start_time IS NOT NULL THEN
              -- Capture active driver at the time of loading start
              SELECT * INTO driver_info
              FROM capture_active_driver_for_trip(truck_id, NEW.loading_start_time);
              
              IF FOUND THEN
                  NEW.performed_by_driver_id := driver_info.driver_id;
                  NEW.performed_by_driver_name := driver_info.driver_name;
                  NEW.performed_by_employee_id := driver_info.employee_id;
                  NEW.performed_by_shift_id := driver_info.shift_id;
                  NEW.performed_by_shift_type := driver_info.shift_type;
              END IF;
          END IF;
          
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    await pool.query(`
      CREATE TRIGGER trigger_auto_capture_trip_driver
          BEFORE INSERT OR UPDATE ON trip_logs
          FOR EACH ROW
          EXECUTE FUNCTION auto_capture_trip_driver();
    `);
    
    console.log('✅ Trigger function created successfully');
    
    // Test the complete setup
    console.log('🧪 Testing complete setup...');
    
    // Check if we have any test data
    const testDataResult = await pool.query(`
      SELECT COUNT(*) as count FROM driver_shifts WHERE status = 'active'
    `);
    
    if (testDataResult.rows[0].count > 0) {
      console.log('✅ Active shifts found for testing');
    } else {
      console.log('⚠️ No active shifts found - creating test data...');
      
      // Create test shift for testing
      const today = new Date().toISOString().split('T')[0];
      await pool.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        SELECT 1, 1, 'day', $1, '08:00:00', '16:00:00', 'active'
        WHERE EXISTS (SELECT 1 FROM dump_trucks WHERE id = 1)
          AND EXISTS (SELECT 1 FROM drivers WHERE id = 1)
        ON CONFLICT (truck_id, shift_date, start_time) DO NOTHING
      `, [today]);
      
      console.log('✅ Test shift created');
    }
    
    // Final test
    const finalTestResult = await pool.query('SELECT * FROM capture_active_driver_for_trip(1)');
    console.log(`🎉 Final test: ${finalTestResult.rows.length} active drivers found`);
    
    if (finalTestResult.rows.length > 0) {
      console.log('👤 Active driver:', finalTestResult.rows[0]);
    }
    
    console.log('\n🎉 Database functions fixed successfully!');
    
  } catch (error) {
    console.log('❌ Fix failed:', error.message);
    console.log('Stack:', error.stack);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  fixDatabaseFunctions().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { fixDatabaseFunctions };
