#!/usr/bin/env node

/**
 * Driver History Test Runner
 * Comprehensive test execution for driver history functionality
 * 
 * Usage: node run-driver-history-tests.js [options]
 * 
 * Options:
 *   --migration-only    Run only database migration tests
 *   --api-only         Run only API tests
 *   --skip-migration   Skip migration application
 *   --verbose          Enable verbose logging
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  backend_port: 5000,
  frontend_port: 3000,
  db_name: 'hauling_system',
  migration_file: 'database/migrations/019_add_trip_driver_history.sql'
};

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  migrationOnly: args.includes('--migration-only'),
  apiOnly: args.includes('--api-only'),
  skipMigration: args.includes('--skip-migration'),
  verbose: args.includes('--verbose')
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    step: '🔄'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function executeCommand(command, description) {
  log(`${description}...`, 'step');
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.verbose ? 'inherit' : 'pipe' 
    });
    log(`${description} completed`, 'success');
    return output;
  } catch (error) {
    log(`${description} failed: ${error.message}`, 'error');
    throw error;
  }
}

async function checkPrerequisites() {
  log('Checking prerequisites...', 'step');
  
  // Check if Node.js modules are available
  try {
    require('pg');
    require('axios');
    log('Required Node.js modules available', 'success');
  } catch (error) {
    log('Installing required Node.js modules...', 'step');
    executeCommand('npm install pg axios', 'Installing dependencies');
  }
  
  // Check if backend is running
  try {
    const { default: axios } = await import('axios');
    await axios.get(`http://localhost:${CONFIG.backend_port}/api/health`);
    log(`Backend server running on port ${CONFIG.backend_port}`, 'success');
  } catch (error) {
    log(`Backend server not accessible on port ${CONFIG.backend_port}`, 'warning');
    log('Please ensure the backend server is running before proceeding', 'warning');
  }
  
  // Check if frontend is accessible
  try {
    const { default: axios } = await import('axios');
    await axios.get(`http://localhost:${CONFIG.frontend_port}`);
    log(`Frontend server running on port ${CONFIG.frontend_port}`, 'success');
  } catch (error) {
    log(`Frontend server not accessible on port ${CONFIG.frontend_port}`, 'warning');
    log('Frontend tests may be limited', 'warning');
  }
}

async function applyMigration() {
  if (options.skipMigration) {
    log('Skipping migration application (--skip-migration flag)', 'warning');
    return;
  }
  
  log('Applying database migration...', 'step');
  
  // Check if migration file exists
  if (!fs.existsSync(CONFIG.migration_file)) {
    log(`Migration file not found: ${CONFIG.migration_file}`, 'error');
    throw new Error('Migration file missing');
  }
  
  try {
    // Use the existing run-migration.js script
    executeCommand('node database/run-migration.js 019_add_trip_driver_history', 'Applying migration');
    log('Database migration applied successfully', 'success');
  } catch (error) {
    log('Migration application failed, attempting manual application...', 'warning');
    
    // Fallback: Apply migration manually
    const { Pool } = require('pg');
    const pool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || CONFIG.db_name,
      password: process.env.DB_PASSWORD || 'password',
      port: process.env.DB_PORT || 5432,
    });
    
    try {
      const migrationSQL = fs.readFileSync(CONFIG.migration_file, 'utf8');
      await pool.query(migrationSQL);
      log('Migration applied manually', 'success');
    } catch (manualError) {
      log(`Manual migration failed: ${manualError.message}`, 'error');
      throw manualError;
    } finally {
      await pool.end();
    }
  }
}

async function runMigrationTests() {
  log('Running migration tests...', 'step');
  
  const { Pool } = require('pg');
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || CONFIG.db_name,
    password: process.env.DB_PASSWORD || 'password',
    port: process.env.DB_PORT || 5432,
  });
  
  try {
    // Test 1: Check if columns exist
    const columnsResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
        AND column_name LIKE 'performed_by_%'
    `);
    
    const expectedColumns = 5; // 5 new columns
    if (columnsResult.rows.length >= expectedColumns) {
      log(`✅ All ${expectedColumns} driver history columns exist`, 'success');
    } else {
      log(`❌ Only ${columnsResult.rows.length}/${expectedColumns} columns found`, 'error');
      throw new Error('Migration incomplete');
    }
    
    // Test 2: Check if indexes exist
    const indexesResult = await pool.query(`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'trip_logs' 
        AND indexname LIKE 'idx_trip_logs_performed_by_%'
    `);
    
    if (indexesResult.rows.length >= 3) {
      log('✅ Performance indexes created', 'success');
    } else {
      log('⚠️ Some performance indexes missing', 'warning');
    }
    
    // Test 3: Check if helper function exists
    const functionResult = await pool.query(`
      SELECT proname 
      FROM pg_proc 
      WHERE proname = 'capture_active_driver_for_trip'
    `);
    
    if (functionResult.rows.length > 0) {
      log('✅ Helper function created', 'success');
    } else {
      log('❌ Helper function missing', 'error');
      throw new Error('Helper function not created');
    }
    
    log('Migration tests completed successfully', 'success');
    
  } catch (error) {
    log(`Migration tests failed: ${error.message}`, 'error');
    throw error;
  } finally {
    await pool.end();
  }
}

async function runApiTests() {
  log('Running API tests...', 'step');
  
  try {
    executeCommand('node test-driver-history.js', 'Running comprehensive API tests');
    log('API tests completed successfully', 'success');
  } catch (error) {
    log(`API tests failed: ${error.message}`, 'error');
    throw error;
  }
}

async function generateTestReport() {
  log('Generating test report...', 'step');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    configuration: CONFIG,
    options: options,
    tests_run: [],
    status: 'completed'
  };
  
  // Add test results if available
  try {
    const testResults = require('./test-driver-history.js').testResults;
    reportData.test_results = testResults;
  } catch (error) {
    reportData.test_results = { note: 'Test results not available' };
  }
  
  const reportPath = `driver-history-test-report-${Date.now()}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  log(`Test report generated: ${reportPath}`, 'success');
  return reportPath;
}

// Main execution
async function main() {
  log('🚀 Starting Driver History Test Runner', 'info');
  log(`Configuration: Backend:${CONFIG.backend_port}, Frontend:${CONFIG.frontend_port}`, 'info');
  
  try {
    await checkPrerequisites();
    
    if (!options.apiOnly) {
      await applyMigration();
      await runMigrationTests();
    }
    
    if (options.migrationOnly) {
      log('Migration-only mode completed', 'success');
      return;
    }
    
    if (!options.skipMigration || options.apiOnly) {
      await runApiTests();
    }
    
    const reportPath = await generateTestReport();
    
    log('🎉 All tests completed successfully!', 'success');
    log(`📊 Test report: ${reportPath}`, 'info');
    
  } catch (error) {
    log(`❌ Test execution failed: ${error.message}`, 'error');
    
    // Generate error report
    const errorReport = {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      configuration: CONFIG,
      options: options
    };
    
    const errorReportPath = `driver-history-error-report-${Date.now()}.json`;
    fs.writeFileSync(errorReportPath, JSON.stringify(errorReport, null, 2));
    
    log(`Error report generated: ${errorReportPath}`, 'info');
    process.exit(1);
  }
}

// Help text
function showHelp() {
  console.log(`
Driver History Test Runner

Usage: node run-driver-history-tests.js [options]

Options:
  --migration-only    Run only database migration tests
  --api-only         Run only API tests (skip migration)
  --skip-migration   Skip migration application
  --verbose          Enable verbose logging
  --help             Show this help message

Examples:
  node run-driver-history-tests.js                    # Run all tests
  node run-driver-history-tests.js --migration-only   # Test migration only
  node run-driver-history-tests.js --api-only         # Test API only
  node run-driver-history-tests.js --verbose          # Verbose output

Prerequisites:
  - Backend server running on port ${CONFIG.backend_port}
  - Frontend server running on port ${CONFIG.frontend_port}
  - PostgreSQL database accessible
  - Node.js with pg and axios modules
`);
}

// Handle command line
if (args.includes('--help') || args.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run main function
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { main, CONFIG };
